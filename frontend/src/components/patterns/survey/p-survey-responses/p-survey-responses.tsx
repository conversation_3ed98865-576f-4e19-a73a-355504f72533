import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from './helpers';
import { ApiWrapper } from '../../../../global/script/helpers/api/ApiWrapper';
import { Var } from '../../../../global/script/var';

@Component({
  tag: 'p-survey-responses',
  styleUrl: 'p-survey-responses.css',
  shadow: true,
})
export class PSurveyResponses {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';
  @State() customStartDate: string = '';
  @State() customEndDate: string = '';
  @State() firstResponseDate: string = '';
  @State() validationError: string = '';

  @State() responses: any[] = [];
  @State() analytics: any = {};
  @State() loading: boolean = true;
  @State() error: string = null;
  @State() selectedResponse: any = null;
  @State() showModal: boolean = false;
  @State() showDeleteModal: boolean = false;
  @State() responseToDelete: string = null;
  @State() deletionReason: string = '';

  // Pagination state
  @State() currentPage: number = 1;
  @State() totalCount: number = 0;
  @State() pageSize: number = 10;

  // Flag to track if data has been fetched
  private isDataFetched: boolean = false;

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
    { label: 'Custom Range', value: 'custom-range' },
  ];

  async componentDidLoad() {
    await this.fetchResponses();
    await this.setFirstResponseDate();
  }

  private async fetchResponses() {
    if (!this.surveyId) return;

    if (this.isDataFetched) {
      this.isDataFetched = false;
    }

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
        page: this.currentPage,
        limit: this.pageSize,
      };

      // Add custom date range if selected
      if (this.selectedTimeFilter === 'custom-range') {
        filters.customStartDate = this.customStartDate;
        filters.customEndDate = this.customEndDate;
      }

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.responses = result.payload.responses || [];
        this.analytics = result.payload || {};
        this.totalCount = result.payload.totalCount || 0;
        this.isDataFetched = true;

        // DEBUG: Log the analytics data to see what we're receiving
        console.log('Analytics data received:', this.analytics);
        console.log('responsesByDay:', this.analytics.responsesByDay);
        console.log('Is responsesByDay an array?', Array.isArray(this.analytics.responsesByDay));

        if (this.analytics.responsesByDay) {
          // Use setTimeout to ensure DOM is updated after loading state change
          setTimeout(async () => {
            await this.renderGraph();
          }, 100);
        }
      } else {
        this.error = result.message;
      }
    } catch (error) {
      this.error = 'Failed to fetch responses';
      console.error('Error fetching responses:', error);
    } finally {
      this.loading = false;
    }
  }

  private async renderGraph() {
    try {
      // DEBUG: Log renderGraph attempt
      console.log('renderGraph called');
      console.log('this.analytics.responsesByDay:', this.analytics.responsesByDay);
      console.log('Is array?', Array.isArray(this.analytics.responsesByDay));

      // Dynamically import Plotly to avoid bundle size issues
      const Plotly = await import('plotly.js-dist-min');

      const graphElement = this.el.shadowRoot.querySelector('#responses-graph');
      console.log('graphElement found:', !!graphElement);

      if (!graphElement) {
        console.log('renderGraph early return - graphElement not found');
        return;
      }

      // Clear any existing plot to ensure proper re-rendering
      try {
        await Plotly.purge(graphElement);
      } catch (error) {
        // Ignore errors if no plot exists to purge
        console.log('No existing plot to purge');
      }

      if (
        !this.analytics.responsesByDay ||
        !Array.isArray(this.analytics.responsesByDay) ||
        this.analytics.responsesByDay.length === 0
      ) {
        console.log('renderGraph early return - no data to render');
        console.log('responsesByDay exists:', !!this.analytics.responsesByDay);
        console.log('responsesByDay is array:', Array.isArray(this.analytics.responsesByDay));
        console.log('responsesByDay length:', this.analytics.responsesByDay?.length);
        return;
      }

      // Prepare data for the bar chart - responsesByDay is now an array of {date, count} objects
      const dates = this.analytics.responsesByDay.map(
        (item: { date: string; count: number }) => item.date,
      );
      const counts = this.analytics.responsesByDay.map(
        (item: { date: string; count: number }) => item.count,
      );

      console.log('Graph data prepared:', { dates, counts });

      const data = [
        {
          x: dates,
          y: counts,
          type: 'bar',
          marker: {
            color: '#7986cb', // One shade lighter indigo (--color__indigo--300)
            line: {
              color: '#7986cb',
              width: 0,
            },
          },
          name: 'Responses',
          hovertemplate: '<b>%{y} responses</b><br>Date: %{x}<extra></extra>',
          hoverlabel: {
            bgcolor: '#3f51b5',
            bordercolor: '#3f51b5',
            font: { color: 'white' },
          },
        },
      ];

      const layout = {
        xaxis: {
          title: 'Date',
          showgrid: false,
          gridcolor: '#f0f0f0',
          // Remove rangemode for x-axis to avoid unnecessary zero
          showticklabels: dates.length > 0, // Hide x-axis labels when no data
        },
        yaxis: {
          title: 'Number of Responses',
          showgrid: true,
          gridcolor: '#f0f0f0',
          zerolinecolor: '#f0f0f0', // Make zero line same color as gridlines
          tickformat: 'd', // Format as integers (no decimals)
          nticks: 5, // Use approximately 5 ticks that auto-adjust
          rangemode: 'tozero',
          showticklabels: dates.length > 0, // Hide y-axis labels when no data
        },
        plot_bgcolor: 'white',
        paper_bgcolor: 'white',
        margin: { t: 0, r: 0, b: 20, l: 20 }, // Reduced margins to maximize chart area
        height: 320,
        width: undefined, // Let it auto-size to container
        autosize: true,
        hovermode: 'closest', // Enable hover mode
      };

      const config = {
        displayModeBar: false,
        responsive: true,
        staticPlot: false,
        scrollZoom: false,
        doubleClick: false,
        displaylogo: false,
        modeBarButtonsToRemove: [
          'pan2d',
          'select2d',
          'lasso2d',
          'resetScale2d',
          'zoomIn2d',
          'zoomOut2d',
        ],
      };

      await Plotly.newPlot(graphElement, data, layout, config);

      // Ensure proper containment after plot is created
      setTimeout(() => {
        const plotlyDiv = graphElement.querySelector('.plotly');
        if (plotlyDiv) {
          (plotlyDiv as HTMLElement).style.position = 'relative';
          (plotlyDiv as HTMLElement).style.zIndex = '1';
          (plotlyDiv as HTMLElement).style.overflow = 'hidden';
        }

        const svgContainer = graphElement.querySelector('.svg-container');
        if (svgContainer) {
          (svgContainer as HTMLElement).style.position = 'relative';
          (svgContainer as HTMLElement).style.zIndex = '1';
          (svgContainer as HTMLElement).style.overflow = 'hidden';
        }

        const svg = graphElement.querySelector('svg');
        if (svg) {
          svg.style.position = 'relative';
          svg.style.zIndex = '1';
          svg.style.maxWidth = '100%';
          svg.style.maxHeight = '100%';
        }

        // CSS hover effects are handled in the stylesheet
      }, 200);

      // Add click event handler to graph points
      (graphElement as any).on('plotly_click', (eventData: any) => {
        console.log('Click event triggered:', eventData);
        if (eventData.points && eventData.points.length > 0) {
          const clickedDate = eventData.points[0].x;
          const responseCount = eventData.points[0].y;

          // Format the date for display
          const formattedDate = new Date(clickedDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });

          // Show alert with date and response count
          console.log(`Date: ${formattedDate}\nResponses: ${responseCount}`);
        }
      });
    } catch (error) {
      console.error('Error rendering graph:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    const newValue = event.detail.value;

    if (newValue === 'custom-range') {
      // Don't open modal, just set the filter - date inputs will show inline
      this.selectedTimeFilter = newValue;
      // Reset dates when switching to custom range
      this.customStartDate = '';
      this.customEndDate = '';
      this.validationError = '';
    } else {
      this.selectedTimeFilter = newValue;
      this.currentPage = 1; // Reset to first page when changing filter
      await this.fetchResponses();
    }
  };

  private getRespondentName(response: any): string {
    return response.respondent_details?.name || response.respondent_details?.firstName || '-';
  }

  private getRespondentEmail(response: any): string {
    return response.respondent_details?.email || '-';
  }

  private formatDate(dateString: string): string {
    const date = new Date(dateString);
    // Format to local time without milliseconds
    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  private formatObjectForDisplay(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const formatKey = (key: string): string => {
      // Convert camelCase to space-separated words
      const spaced = key.replace(/([A-Z])/g, ' $1');
      // Convert to uppercase
      return spaced.toUpperCase().trim();
    };

    const formatValue = (value: any, parentKey: string): any => {
      // Handle empty, null, undefined, or empty string values
      if (
        value === null ||
        value === undefined ||
        value === '' ||
        (typeof value === 'string' && value.trim() === '')
      ) {
        return '-';
      }

      // Handle nested objects (like userAgent) - return as array for list rendering
      if (typeof value === 'object' && value !== null) {
        return Object.entries(value)
          .filter(([nestedKey, _]) => {
            // Filter out specific fields based on parent key
            if (parentKey.toLowerCase() === 'useragent') {
              return nestedKey !== 'onLine' && nestedKey !== 'timestamp';
            }
            return true;
          })
          .map(([nestedKey, nestedValue]) => ({
            key: nestedKey,
            value: String(nestedValue), // Convert all values to strings to ensure visibility
          }));
      }

      return value;
    };

    return Object.entries(obj)
      .filter(([key, _]) => {
        // Filter out IP address from metadata
        return key.toLowerCase() !== 'ipaddress';
      })
      .map(([key, value]) => ({
        label: formatKey(key),
        value: formatValue(value, key),
      }));
  }

  private renderValue(value: any) {
    // If value is an array of key-value pairs (nested object), render as list
    if (
      Array.isArray(value) &&
      value.length > 0 &&
      typeof value[0] === 'object' &&
      'key' in value[0]
    ) {
      return (
        <ul style={{ margin: '0', paddingLeft: '1.5em' }}>
          {value.map((item: any) => (
            <li key={item.key}>
              <e-text>
                {item.key}: {item.value}
              </e-text>
            </li>
          ))}
        </ul>
      );
    }

    // For simple values, render as text
    return <e-text>{value}</e-text>;
  }

  private openResponseModal = (response: any) => {
    this.selectedResponse = response;
    this.showModal = true;
  };

  private closeModal = () => {
    this.showModal = false;
    this.selectedResponse = null;
  };

  private validateDateRange = (): boolean => {
    this.validationError = '';

    if (!this.customStartDate || !this.customEndDate) {
      this.validationError = 'Please select both start and end dates.';
      return false;
    }

    const startDate = new Date(this.customStartDate);
    const endDate = new Date(this.customEndDate);
    const today = new Date();
    const firstResponse = new Date(this.firstResponseDate);

    if (startDate > endDate) {
      this.validationError = 'Start date must be before or equal to end date.';
      return false;
    }

    if (startDate > today) {
      this.validationError = 'Start date cannot be in the future.';
      return false;
    }

    if (endDate > today) {
      this.validationError = 'End date cannot be in the future.';
      return false;
    }

    if (this.firstResponseDate && startDate < firstResponse) {
      this.validationError = 'Start date cannot be before the first response date.';
      return false;
    }

    // Check if date range is too large (more than 2 years)
    const daysDifference = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDifference > 730) {
      this.validationError = 'Date range cannot exceed 2 years.';
      return false;
    }

    return true;
  };

  private async setFirstResponseDate() {
    if (this.analytics.oldestResponseTimestamp) {
      // Use the oldest response timestamp from the API
      this.firstResponseDate = this.analytics.oldestResponseTimestamp.split('T')[0]; // Get just the date part
    } else {
      // Default to 30 days ago if no responses
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      this.firstResponseDate = thirtyDaysAgo.toISOString().split('T')[0];
    }
  }

  // New method for inline date range application
  private applyInlineCustomDateRange = async () => {
    if (!this.validateDateRange()) {
      return;
    }

    this.currentPage = 1; // Reset to first page when applying new filter
    await this.fetchResponses();
  };

  // Pagination methods
  private goToPage = async (page: number) => {
    this.currentPage = page;
    await this.fetchResponses();
  };

  private goToPreviousPage = async () => {
    if (this.currentPage > 1) {
      this.currentPage--;
      await this.fetchResponses();
    }
  };

  private goToNextPage = async () => {
    const totalPages = Math.ceil(this.totalCount / this.pageSize);
    if (this.currentPage < totalPages) {
      this.currentPage++;
      await this.fetchResponses();
    }
  };

  private getTotalPages(): number {
    return Math.ceil(this.totalCount / this.pageSize);
  }

  private getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, this.currentPage - 2);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  }

  private deleteResponse = (responseId: string) => {
    this.responseToDelete = responseId;
    this.showDeleteModal = true;
    this.deletionReason = '';
  };

  private confirmDeleteResponse = async () => {
    if (!this.deletionReason.trim()) {
      alert('Please provide a reason for deletion.');
      return;
    }

    try {
      // Call the delete response API
      const result = await this.deleteResponseApi(this.responseToDelete, this.deletionReason);

      if (result.success) {
        // Show success message
        alert(result.message || 'Response deleted successfully');
        // Reload the responses to reflect the deletion
        await this.fetchResponses();
        this.closeDeleteModal();
      } else {
        // Show error message
        alert(result.message || 'Failed to delete response. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting response:', error);
      alert('Failed to delete response. Please try again.');
    }
  };

  private closeDeleteModal = () => {
    this.showDeleteModal = false;
    this.responseToDelete = null;
    this.deletionReason = '';
  };

  private deleteResponseApi = async (responseId: string, reason: string) => {
    try {
      const result = await ApiWrapper(`/responses/${responseId}`, {
        method: 'DELETE',
        body: { reason },
        includeCsrf: true, // Include CSRF token for data modification requests
      });

      return {
        success: result.success,
        message: result.message,
        payload: result.payload,
      };
    } catch (error) {
      console.error('Error deleting response:', error);
      return {
        success: false,
        message: 'Error deleting response',
        payload: null,
      };
    }
  };

  private getTrendTooltipText(): string {
    const trendComponents = this.analytics.responseRateTrendComponents;
    const headerText = this.analytics.headerText || '';

    if (!trendComponents) {
      return 'Percentage change for the selected time range vs the previous time range of the same value';
    }

    // Create detailed explanation based on the trend type
    let baseExplanation = '';

    if (headerText.includes('24-HOUR')) {
      baseExplanation = 'Compares responses in the last 24 hours vs the previous 24 hours';
    } else if (headerText.includes('7-DAY')) {
      baseExplanation = 'Compares responses in the last 7 days vs the previous 7 days';
    } else if (headerText.includes('30-DAY')) {
      baseExplanation = 'Compares responses in the last 30 days vs the previous 30 days';
    } else if (headerText.includes('90-DAY')) {
      baseExplanation = 'Compares responses in the last 90 days vs the previous 90 days';
    } else if (headerText.includes('CUSTOM')) {
      baseExplanation =
        'Compares responses in the selected period vs the previous period of the same duration';
    } else {
      baseExplanation =
        'Percentage change for the selected time range vs the previous time range of the same value';
    }

    // Add calculation details
    const calculationDetails =
      '\n\nCalculation: ((Current Period - Previous Period) / Previous Period) × 100%';

    // Add special case explanations
    let specialCases = '';
    if (
      trendComponents.direction === 'neutral' &&
      trendComponents.comparisonText === 'responses/day avg'
    ) {
      specialCases =
        "\n\nShowing average responses per day when there's no change from the previous period.";
    } else if (trendComponents.comparisonText === 'New responses this period') {
      specialCases =
        '\n\nShowing "New responses" when there were no responses in the previous period.';
    } else if (trendComponents.comparisonText === 'No responses yet') {
      specialCases = '\n\nNo responses in either the current or previous period.';
    }

    return baseExplanation + calculationDetails + specialCases;
  }

  private renderTrendDisplay() {
    // Use new structured trend components if available, fallback to legacy string
    const trendComponents = this.analytics.responseRateTrendComponents;

    if (!trendComponents) {
      // Fallback to legacy string format
      return (
        <e-text variant="display" style={{ fontSize: '1em' }}>
          {this.analytics.responseRateTrend || 'No data'}
        </e-text>
      );
    }

    // Determine color based on direction
    const getColor = (direction: string) => {
      switch (direction) {
        case 'positive':
          return 'var(--color__green--600)'; // Green for positive trends
        case 'negative':
          return 'var(--color__red--600)'; // Red for negative trends
        case 'neutral':
          return 'inherit'; // Default font color for neutral
        default:
          return 'var(--color__grey--600)'; // Grey for other cases
      }
    };

    // Determine sign based on direction
    const getSign = (direction: string, magnitude: number) => {
      if (magnitude === 0) return '';
      switch (direction) {
        case 'positive':
          return '+';
        case 'negative':
          return '-';
        default:
          return '';
      }
    };

    const color = getColor(trendComponents.direction);
    const sign = getSign(trendComponents.direction, trendComponents.magnitude);

    return (
      <div>
        {trendComponents.direction === 'neutral' ? (
          // Special handling for neutral direction - show average response per day
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              fontWeight: 'bold',
            }}
          >
            {this.analytics.avgResponsesPerDay || 0}
          </e-text>
        ) : trendComponents.magnitude > 0 ? (
          // Show only the percentage value without comparison text
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              color: color,
              fontWeight: 'bold',
            }}
          >
            {sign}
            {trendComponents.magnitude}%
          </e-text>
        ) : (
          <e-text
            variant="display"
            style={{
              fontSize: '1em',
              color: color,
              fontWeight: 'bold',
            }}
          >
            {trendComponents.comparisonText}
          </e-text>
        )}
      </div>
    );
  }

  private renderSkeletonCards() {
    return (
      <l-row>
        <c-card style={{ flex: '1', marginRight: '1em' }}>
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                height: '3em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
        <c-card style={{ flex: '1', marginRight: '1em' }}>
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                height: '3em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
        <c-card style={{ flex: '1' }}>
          <div style={{ textAlign: 'center' }}>
            <div
              style={{
                height: '3em',
                backgroundColor: 'var(--color__grey--100)',
                borderRadius: '4px',
                animation: 'pulse 1.5s ease-in-out infinite',
              }}
            ></div>
          </div>
        </c-card>
      </l-row>
    );
  }

  private renderSkeletonGraph() {
    return (
      <c-card style={{ padding: '0' }}>
        <div
          style={{
            width: '100%',
            height: '320px',
            backgroundColor: 'var(--color__grey--100)',
            borderRadius: '4px',
            animation: 'pulse 1.5s ease-in-out infinite',
          }}
        ></div>
      </c-card>
    );
  }

  render() {
    return (
      <div>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Responses</e-text>

          {/* Custom Range Date Inputs - shown inline when custom range is selected */}
          {this.selectedTimeFilter === 'custom-range' && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75em',
                flex: '1',
                justifyContent: 'center',
              }}
            >
              <input
                type="date"
                value={this.customStartDate}
                min={this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customStartDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />

              <e-text style={{ color: 'var(--color__grey--600)', fontSize: '0.875em' }}>to</e-text>

              <input
                type="date"
                value={this.customEndDate}
                min={this.customStartDate || this.firstResponseDate}
                max={new Date().toISOString().split('T')[0]}
                onInput={e => {
                  this.customEndDate = (e.target as HTMLInputElement).value;
                  this.validationError = '';
                  // Auto-apply when both dates are set
                  if (this.customStartDate && this.customEndDate) {
                    this.applyInlineCustomDateRange();
                  }
                }}
                style={{
                  padding: '0.5em',
                  border: 'var(--border__input)',
                  borderRadius: 'var(--border-radius)',
                  fontSize: '0.875em',
                  width: '140px',
                }}
              />
            </div>
          )}

          <div style={{ position: 'relative' }}>
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              variant="narrow"
              onSelectChangeEvent={this.handleTimeFilterChange}
            />
          </div>
        </l-row>

        {/* Validation Error for inline custom range */}
        {this.selectedTimeFilter === 'custom-range' && this.validationError && (
          <div style={{ marginTop: '0.5em', textAlign: 'center' }}>
            <e-text
              style={{
                color: 'var(--color__red--600)',
                fontSize: '0.875em',
                display: 'inline-block',
                padding: '0.5em 1em',
                backgroundColor: 'var(--color__red--50)',
                border: '1px solid var(--color__red--200)',
                borderRadius: 'var(--border-radius)',
              }}
            >
              {this.validationError}
            </e-text>
          </div>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text>
                <strong>{this.error}</strong>
              </e-text>
            </div>
          </c-card>
        )}

        {/* Overview Cards - Show skeleton while loading, real data when loaded */}
        {this.loading ? (
          this.renderSkeletonCards()
        ) : (
          <l-row>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">TOTAL RESPONSES</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.totalCount || 0}</e-text>
              </div>
            </c-card>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5em',
                  }}
                >
                  <e-text variant="footnote">
                    {this.analytics.headerText || 'AVG. RESPONSES PER DAY'}
                  </e-text>
                  {this.analytics.headerText &&
                    this.analytics.headerText !== 'AVG. RESPONSES PER DAY' && (
                      <e-image
                        src="../../../assets/icon/light/info-light.svg"
                        width="1em"
                        title={this.getTrendTooltipText()}
                        style={{ cursor: 'help' }}
                      ></e-image>
                    )}
                </div>
                <l-spacer value={0.25}></l-spacer>
                {this.renderTrendDisplay()}
              </div>
            </c-card>
            <c-card style={{ flex: '1' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">AVG. COMPLETION TIME</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.analytics.avgCompletionTime || 0}s</e-text>
              </div>
            </c-card>
          </l-row>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Graph - Show skeleton while loading, real graph when loaded */}
        {this.loading ? (
          this.renderSkeletonGraph()
        ) : (
          <c-card style={{ padding: '0' }}>
            <e-text variant="footnote">RESPONSES OVER TIME</e-text>
            <l-spacer value={0.5}></l-spacer>
            <div
              id="responses-graph"
              style={{
                width: '100%',
                height: '320px',
                position: 'relative',
                overflow: 'hidden',
                zIndex: '1',
                display:
                  this.analytics.responsesByDay &&
                  Array.isArray(this.analytics.responsesByDay) &&
                  this.analytics.responsesByDay.length > 0
                    ? 'block'
                    : 'none',
              }}
            ></div>
            {(!this.analytics.responsesByDay ||
              !Array.isArray(this.analytics.responsesByDay) ||
              this.analytics.responsesByDay.length === 0) && (
              <div style={{ padding: '2em', textAlign: 'center' }}>
                <e-text>No response data available for chart</e-text>
              </div>
            )}
          </c-card>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Responses Content */}
        <c-card>
          <div class="response-table">
            <div class="table-header">
              <div class="table-cell">
                <e-text variant="footnote">NAME</e-text>
              </div>
              <div class="table-cell">
                <e-text variant="footnote">EMAIL</e-text>
              </div>
              <div class="table-cell">
                <e-text variant="footnote">SUBMITTED ON</e-text>
              </div>
            </div>
            <l-separator></l-separator>

            <div class="table-body">
              {this.loading ? (
                // Show skeleton rows while loading
                <div>
                  {[1, 2, 3].map(index => (
                    <div key={index} class="table-row">
                      <div class="table-cell">
                        <div
                          style={{
                            height: '1.2em',
                            backgroundColor: 'var(--color__grey--100)',
                            borderRadius: '4px',
                            animation: 'pulse 1.5s ease-in-out infinite',
                          }}
                        ></div>
                      </div>
                      <div class="table-cell">
                        <div
                          style={{
                            height: '1.2em',
                            backgroundColor: 'var(--color__grey--100)',
                            borderRadius: '4px',
                            animation: 'pulse 1.5s ease-in-out infinite',
                          }}
                        ></div>
                      </div>
                      <div class="table-cell">
                        <div
                          style={{
                            height: '1.2em',
                            backgroundColor: 'var(--color__grey--100)',
                            borderRadius: '4px',
                            animation: 'pulse 1.5s ease-in-out infinite',
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : this.responses.length === 0 ? (
                // Show empty state within table structure
                <div
                  class="table-row table-row--no-response"
                  style={{ textAlign: 'center', padding: '2em 0' }}
                >
                  <div style={{ width: '100%', textAlign: 'center' }}>
                    <e-text>
                      <strong>No Responses Yet</strong>
                      <br />
                      Data will appear here once participants start responding to the survey
                    </e-text>
                  </div>
                </div>
              ) : (
                // Show actual response data
                this.responses.map(response => (
                  <div
                    key={response.id}
                    class="table-row"
                    onClick={() => this.openResponseModal(response)}
                  >
                    <div class="table-cell">{this.getRespondentName(response)}</div>
                    <div class="table-cell">{this.getRespondentEmail(response)}</div>
                    <div class="table-cell">{this.formatDate(response.created_at)}</div>
                    <div class="table-cell-action">
                      <button
                        class="delete-button"
                        onClick={e => {
                          e.stopPropagation();
                          this.deleteResponse(response.id);
                        }}
                        title="Delete response"
                      >
                        <e-image src="../../../assets/icon/red/trash-red.svg" width="1em"></e-image>
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Pagination Controls */}
          {this.totalCount > this.pageSize && (
            <div class="pagination-controls">
              <l-row justifyContent="space-between" align="center">
                <e-text variant="footnote">
                  Showing {(this.currentPage - 1) * this.pageSize + 1} to{' '}
                  {Math.min(this.currentPage * this.pageSize, this.totalCount)} of {this.totalCount}{' '}
                  responses
                </e-text>

                <l-row align="center">
                  <button
                    class="pagination-button"
                    onClick={this.goToPreviousPage}
                    disabled={this.currentPage === 1}
                  >
                    &lt; Previous
                  </button>

                  {this.getPageNumbers().map(pageNum => (
                    <button
                      key={pageNum}
                      class={`pagination-number ${pageNum === this.currentPage ? 'active' : ''}`}
                      onClick={() => this.goToPage(pageNum)}
                    >
                      {pageNum}
                    </button>
                  ))}

                  <button
                    class="pagination-button pagination-button--last"
                    onClick={this.goToNextPage}
                    disabled={this.currentPage === this.getTotalPages()}
                  >
                    Next &gt;
                  </button>
                </l-row>
              </l-row>
            </div>
          )}
        </c-card>

        {/* Response Detail Modal */}
        <p-modal
          is-open={this.showModal}
          modal-title="Response Details"
          onModalCloseEvent={this.closeModal}
        >
          {this.showModal && this.selectedResponse && (
            <div>
              <e-text variant="footnote">
                Submitted On: {this.formatDate(this.selectedResponse.created_at)}
              </e-text>

              {/* Always show name and email */}
              <l-spacer value={1}></l-spacer>
              <c-card>
                <div class="response-section">
                  <e-text>
                    <strong>Respondent Information</strong>
                  </e-text>
                  <l-spacer value={1}></l-spacer>
                  <div style={{ marginBottom: '1em' }}>
                    <e-text variant="footnote">NAME</e-text>
                    <e-text>{this.getRespondentName(this.selectedResponse)}</e-text>
                  </div>
                  <div style={{ marginBottom: '1em' }}>
                    <e-text variant="footnote">EMAIL</e-text>
                    <e-text>{this.getRespondentEmail(this.selectedResponse)}</e-text>
                  </div>
                </div>
              </c-card>

              {this.selectedResponse.respondent_details && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Respondent Details</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.respondent_details).map(
                        (item: any) => (
                          <div style={{ marginBottom: '1em' }}>
                            <e-text variant="footnote">{item.label}</e-text>
                            {this.renderValue(item.value)}
                          </div>
                        ),
                      )}
                    </div>
                  </c-card>
                </div>
              )}

              <l-spacer value={1.5}></l-spacer>
              <c-card>
                <div class="response-section">
                  <e-text>
                    <strong>Response Data</strong>
                  </e-text>
                  <l-spacer value={1}></l-spacer>
                  {this.formatObjectForDisplay(this.selectedResponse.response_data).map(
                    (item: any) => (
                      <div style={{ marginBottom: '1em' }}>
                        <e-text variant="footnote">{item.label}</e-text>
                        {this.renderValue(item.value)}
                      </div>
                    ),
                  )}
                </div>
              </c-card>

              {this.selectedResponse.meta && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Metadata</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.meta).map((item: any) => (
                        <div style={{ marginBottom: '1em' }}>
                          <e-text variant="footnote">{item.label}</e-text>
                          {this.renderValue(item.value)}
                        </div>
                      ))}
                    </div>
                  </c-card>
                </div>
              )}
            </div>
          )}
        </p-modal>

        {/* Delete Response Modal */}
        <p-modal
          is-open={this.showDeleteModal}
          modal-title="Delete Response"
          onModalCloseEvent={this.closeDeleteModal}
        >
          {this.showDeleteModal && (
            <div>
              <e-text>
                <strong>Are you sure you want to delete this response?</strong>
              </e-text>
              <l-spacer value={1}></l-spacer>
              <e-text>This action cannot be undone. Please provide a reason for deletion:</e-text>
              <l-spacer value={1}></l-spacer>

              <div style={{ marginBottom: '1em' }}>
                <e-text variant="footnote">DELETION REASON *</e-text>
                <l-spacer value={0.5}></l-spacer>
                <textarea
                  value={this.deletionReason}
                  onInput={e => {
                    this.deletionReason = (e.target as HTMLTextAreaElement).value;
                  }}
                  placeholder="Enter reason for deleting this response..."
                  style={{
                    width: '100%',
                    minHeight: '80px',
                    padding: '0.5em',
                    border: '1px solid var(--color__grey--300)',
                    borderRadius: '4px',
                    fontFamily: 'inherit',
                    fontSize: 'inherit',
                    resize: 'vertical',
                    boxSizing: 'border-box',
                  }}
                />
              </div>

              <l-row justifyContent="flex-end">
                <e-button variant="ghost" onClick={this.closeDeleteModal}>
                  Cancel
                </e-button>
                <div style={{ width: '1em' }}></div>
                <e-button theme="danger" onClick={this.confirmDeleteResponse}>
                  Delete Response
                </e-button>
              </l-row>
            </div>
          )}
        </p-modal>
      </div>
    );
  }
}
